import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { NavigationItem } from '../../types/dashboard';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { cn } from '../../lib/utils';
import { PanelLeft } from 'lucide-react';

interface CollapsibleSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  activeSection: string;
  navigationItems: NavigationItem[];
  onNavigate: (section: string) => void;
}

export const CollapsibleSidebar: React.FC<CollapsibleSidebarProps> = ({
  isCollapsed,
  onToggle,
  activeSection,
  navigationItems,
  onNavigate
}) => {
  const location = useLocation();

  const handleNavigation = (item: NavigationItem) => {
    onNavigate(item.id);
    // Don't navigate if we're already on the dashboard and clicking dashboard
    if (item.path !== '/dashboard') {
      window.location.href = item.path;
    }
  };

  const isItemActive = (item: NavigationItem) => {
    // Special handling for dashboard route
    if (item.id === 'dashboard') {
      return location.pathname === '/dashboard' || activeSection === 'dashboard';
    }
    return location.pathname.startsWith(item.path) || activeSection === item.id;
  };

  return (
    <aside
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transition-all duration-300 z-40 flex flex-col",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      {/* IsotopeAI Logo and Branding */}
      <div className={cn(
        "p-4 border-b border-gray-200 dark:border-gray-800 flex items-center gap-3",
        isCollapsed && "justify-center p-3"
      )}>
        {!isCollapsed ? (
          <Link to="/" className="flex items-center gap-3 group hover:opacity-80 transition-opacity">
            <div className="relative w-8 h-8">
              <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
              <img
                src="/icon-192x192.png"
                alt="IsotopeAI Logo"
                className="w-full h-full rounded-full border border-gray-200 dark:border-gray-700 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="flex flex-col">
              <span className="font-bold text-lg text-gray-900 dark:text-white tracking-tight">
                IsotopeAI
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                Focus. Track. Achieve.
              </span>
            </div>
          </Link>
        ) : (
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <Link to="/" className="group">
                <div className="relative w-8 h-8">
                  <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
                  <img
                    src="/icon-192x192.png"
                    alt="IsotopeAI Logo"
                    className="w-full h-full rounded-full border border-gray-200 dark:border-gray-700 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right" className="ml-2">
              <div className="text-center">
                <p className="font-semibold">IsotopeAI</p>
                <p className="text-xs text-gray-400">Focus. Track. Achieve.</p>
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4 pt-6 space-y-2 overflow-y-auto">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = isItemActive(item);

          const buttonContent = (
            <Button
              variant={isActive ? "secondary" : "ghost"}
              className={cn(
                "w-full transition-all duration-200",
                isCollapsed ? "justify-center px-2" : "justify-start px-4",
                isActive && "bg-violet-50 dark:bg-violet-900/20 text-violet-600 dark:text-violet-400 border-violet-200 dark:border-violet-800"
              )}
              onClick={() => handleNavigation(item)}
            >
              <div className="flex items-center gap-3 w-full">
                <Icon
                  size={20}
                  className={cn(
                    "flex-shrink-0",
                    isActive ? "text-violet-600 dark:text-violet-400" : "text-gray-600 dark:text-gray-400"
                  )}
                />

                {!isCollapsed && (
                  <>
                    <span className={cn(
                      "font-medium text-sm truncate",
                      isActive ? "text-violet-600 dark:text-violet-400" : "text-gray-700 dark:text-gray-300"
                    )}>
                      {item.label}
                    </span>

                    {item.badge && item.badge > 0 && (
                      <Badge
                        variant="secondary"
                        className="ml-auto bg-violet-100 dark:bg-violet-900 text-violet-600 dark:text-violet-400 text-xs"
                      >
                        {item.badge > 99 ? '99+' : item.badge}
                      </Badge>
                    )}
                  </>
                )}
              </div>
            </Button>
          );

          if (isCollapsed) {
            return (
              <Tooltip key={item.id} delayDuration={0}>
                <TooltipTrigger asChild>
                  <div className="relative">
                    {buttonContent}
                    {item.badge && item.badge > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0 min-w-[20px]"
                      >
                        {item.badge > 9 ? '9+' : item.badge}
                      </Badge>
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="ml-2">
                  <p>{item.label}</p>
                  {item.badge && item.badge > 0 && (
                    <p className="text-xs text-gray-400">
                      {item.badge} {item.badge === 1 ? 'item' : 'items'}
                    </p>
                  )}
                </TooltipContent>
              </Tooltip>
            );
          }

          return (
            <div key={item.id}>
              {buttonContent}
            </div>
          );
        })}
      </nav>

      {/* Sidebar Footer with Toggle and Study Streak */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-800 space-y-3">
        {/* Study Streak Card */}
        <div className={cn(
          "p-3 rounded-lg bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 border border-emerald-200 dark:border-emerald-800",
          isCollapsed && "p-2"
        )}>
          {!isCollapsed ? (
            <div>
              <p className="text-sm font-medium text-emerald-900 dark:text-emerald-100 mb-1">
                Study Streak
              </p>
              <p className="text-xs text-emerald-700 dark:text-emerald-300">
                7 days and counting! 🔥
              </p>
            </div>
          ) : (
            <div className="flex justify-center">
              <span className="text-lg">🔥</span>
            </div>
          )}
        </div>

        {/* Sidebar Toggle Button */}
        <div className="flex justify-center">
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className={cn(
                  "p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 rounded-lg border border-gray-200 dark:border-gray-700",
                  isCollapsed ? "w-10 h-10" : "w-full"
                )}
              >
                <PanelLeft
                  className={cn(
                    "h-4 w-4 text-gray-600 dark:text-gray-400 transition-transform duration-200",
                    isCollapsed && "rotate-180"
                  )}
                />
                {!isCollapsed && (
                  <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Collapse
                  </span>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side={isCollapsed ? "right" : "top"} className={isCollapsed ? "ml-2" : ""}>
              <p>{isCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </aside>
  );
};

export default CollapsibleSidebar;
