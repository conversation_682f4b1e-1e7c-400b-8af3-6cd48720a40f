import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { NavigationItem } from '../../types/dashboard';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { cn } from '../../lib/utils';

interface CollapsibleSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  activeSection: string;
  navigationItems: NavigationItem[];
  onNavigate: (section: string) => void;
}

export const CollapsibleSidebar: React.FC<CollapsibleSidebarProps> = ({
  isCollapsed,
  onToggle,
  activeSection,
  navigationItems,
  onNavigate
}) => {
  const location = useLocation();

  const handleNavigation = (item: NavigationItem) => {
    onNavigate(item.id);
    // Don't navigate if we're already on the dashboard and clicking dashboard
    if (item.path !== '/dashboard') {
      window.location.href = item.path;
    }
  };

  const isItemActive = (item: NavigationItem) => {
    // Special handling for dashboard route
    if (item.id === 'dashboard') {
      return location.pathname === '/dashboard' || activeSection === 'dashboard';
    }
    return location.pathname.startsWith(item.path) || activeSection === item.id;
  };

  return (
    <aside
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transition-all duration-300 z-40",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <nav className="p-4 pt-8 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = isItemActive(item);

          const buttonContent = (
            <Button
              variant={isActive ? "secondary" : "ghost"}
              className={cn(
                "w-full transition-all duration-200",
                isCollapsed ? "justify-center px-2" : "justify-start px-4",
                isActive && "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800"
              )}
              onClick={() => handleNavigation(item)}
            >
              <div className="flex items-center gap-3 w-full">
                <Icon
                  size={20}
                  className={cn(
                    "flex-shrink-0",
                    isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-600 dark:text-gray-400"
                  )}
                />

                {!isCollapsed && (
                  <>
                    <span className={cn(
                      "font-medium text-sm truncate",
                      isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-700 dark:text-gray-300"
                    )}>
                      {item.label}
                    </span>

                    {item.badge && item.badge > 0 && (
                      <Badge
                        variant="secondary"
                        className="ml-auto bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 text-xs"
                      >
                        {item.badge > 99 ? '99+' : item.badge}
                      </Badge>
                    )}
                  </>
                )}
              </div>
            </Button>
          );

          if (isCollapsed) {
            return (
              <Tooltip key={item.id} delayDuration={0}>
                <TooltipTrigger asChild>
                  <div className="relative">
                    {buttonContent}
                    {item.badge && item.badge > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0 min-w-[20px]"
                      >
                        {item.badge > 9 ? '9+' : item.badge}
                      </Badge>
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="ml-2">
                  <p>{item.label}</p>
                  {item.badge && item.badge > 0 && (
                    <p className="text-xs text-gray-400">
                      {item.badge} {item.badge === 1 ? 'item' : 'items'}
                    </p>
                  )}
                </TooltipContent>
              </Tooltip>
            );
          }

          return (
            <div key={item.id}>
              {buttonContent}
            </div>
          );
        })}
      </nav>

      {/* Sidebar Footer */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className={cn(
          "p-3 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800",
          isCollapsed && "p-2"
        )}>
          {!isCollapsed ? (
            <div>
              <p className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Study Streak
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                7 days and counting! 🔥
              </p>
            </div>
          ) : (
            <div className="flex justify-center">
              <span className="text-lg">🔥</span>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};

export default CollapsibleSidebar;
